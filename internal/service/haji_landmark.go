// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IHajiLandmark interface {
		TypeList(ctx context.Context, req *v1.HajiLandmarkTypeListReq) (out *v1.HajiLandmarkTypeListRes, err error)
		TypeAdd(ctx context.Context, req *v1.HajiLandmarkTypeCreateReq) (out *v1.HajiLandmarkTypeCreateRes, err error)
		TypeEdit(ctx context.Context, req *v1.HajiLandmarkTypeEditReq) (out *v1.HajiLandmarkTypeEditRes, err error)
		TypeOne(ctx context.Context, req *v1.HajiLandmarkTypeOneReq) (res *v1.HajiLandmarkTypeOneRes, err error)
		TypeDelete(ctx context.Context, req *v1.HajiLandmarkTypeDeleteReq) (out *v1.HajiLandmarkTypeDeleteRes, err error)
		List(ctx context.Context, req *v1.HajiLandmarkListReq) (out *v1.HajiLandmarkListRes, err error)
		Add(ctx context.Context, req *v1.HajiLandmarkCreateReq) (out *v1.HajiLandmarkCreateRes, err error)
		Edit(ctx context.Context, req *v1.HajiLandmarkEditReq) (out *v1.HajiLandmarkEditRes, err error)
		One(ctx context.Context, req *v1.HajiLandmarkOneReq) (res *v1.HajiLandmarkOneRes, err error)
		Delete(ctx context.Context, req *v1.HajiLandmarkDeleteReq) (out *v1.HajiLandmarkDeleteRes, err error)
	}
)

var (
	localHajiLandmark IHajiLandmark
)

func HajiLandmark() IHajiLandmark {
	if localHajiLandmark == nil {
		panic("implement not found for interface IHajiLandmark, forgot register?")
	}
	return localHajiLandmark
}

func RegisterHajiLandmark(i IHajiLandmark) {
	localHajiLandmark = i
}
