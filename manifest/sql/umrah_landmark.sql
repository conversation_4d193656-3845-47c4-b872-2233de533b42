create table islamic_content_svc.umrah_landmark
(
    id          bigint unsigned auto_increment comment '主键ID'
        primary key,
    type_id     bigint unsigned                       not null comment '地标类型ID，关联umrah_landmark_type.id',
    inner_type  varchar(100)      default 'destinasi' not null comment '内部类型: (destinasi, tokoh)',
    latitude    decimal(11, 8)                        null comment '纬度',
    longitude   decimal(11, 8)                        null comment '经度',
    image_url   varchar(500)                          null comment '图片URL',
    sort_order  smallint unsigned default '0'         not null comment '排序值，数字越小排序越靠前',
    create_time bigint unsigned   default '0'         not null comment '创建时间（毫秒时间戳）',
    update_time bigint unsigned   default '0'         not null comment '更新时间（毫秒时间戳）',
    constraint umrah_landmark_ibfk_1
        foreign key (type_id) references islamic_content_svc.umrah_landmark_type (id)
            on update cascade
)
    comment '副朝地标表';

create index idx_inner_type
    on islamic_content_svc.umrah_landmark (inner_type)
    comment '内部类型索引';

-- comment on index islamic_content_svc.idx_inner_type not supported: 内部类型索引

create index idx_sort_order
    on islamic_content_svc.umrah_landmark (sort_order)
    comment '排序索引';

-- comment on index islamic_content_svc.idx_sort_order not supported: 排序索引

create index idx_type_id
    on islamic_content_svc.umrah_landmark (type_id)
    comment '地标类型ID索引';

-- comment on index islamic_content_svc.idx_type_id not supported: 地标类型ID索引

create table islamic_content_svc.umrah_landmark_languages
(
    id                bigint unsigned auto_increment comment '主键ID'
        primary key,
    landmark_id       bigint unsigned              not null comment '地标ID，关联umrah_landmark.id',
    language_id       tinyint unsigned default '0' not null comment '语言ID：0-中文，1-英文，2-印尼语',
    landmark_name     varchar(255)                 not null comment '地标名称',
    country           varchar(100)                 not null comment '国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)',
    address           text                         not null comment '详细地址',
    short_description text                         null comment '简介',
    information_text  longtext                     null comment '详细介绍',
    create_time       bigint unsigned  default '0' not null comment '创建时间（毫秒时间戳）',
    update_time       bigint unsigned  default '0' not null comment '更新时间（毫秒时间戳）',
    constraint uk_landmark_language
        unique (landmark_id, language_id) comment '地标ID和语言唯一索引',
    constraint umrah_landmark_languages_ibfk_1
        foreign key (landmark_id) references islamic_content_svc.umrah_landmark (id)
            on update cascade on delete cascade
)
    comment '副朝地标多语言表';

-- comment on index islamic_content_svc.uk_landmark_language not supported: 地标ID和语言唯一索引

create index idx_landmark_id
    on islamic_content_svc.umrah_landmark_languages (landmark_id)
    comment '地标ID索引';

-- comment on index islamic_content_svc.idx_landmark_id not supported: 地标ID索引

create index idx_language_id
    on islamic_content_svc.umrah_landmark_languages (language_id)
    comment '语言ID索引';

-- comment on index islamic_content_svc.idx_language_id not supported: 语言ID索引

create table islamic_content_svc.umrah_landmark_type
(
    id          bigint unsigned auto_increment comment '主键ID'
        primary key,
    icon_url    varchar(255)                null comment '图标路径',
    create_time bigint unsigned default '0' not null comment '创建时间（毫秒时间戳）',
    update_time bigint unsigned default '0' not null comment '更新时间（毫秒时间戳）'
)
    comment '副朝地标类型表';

create table islamic_content_svc.umrah_landmark_type_languages
(
    id          bigint unsigned auto_increment comment '主键ID'
        primary key,
    type_id     bigint unsigned              not null comment '地标类型ID，关联umrah_landmark_type.id',
    language_id tinyint unsigned default '0' not null comment '语言ID：0-中文，1-英文，2-印尼语',
    type_name   varchar(100)                 not null comment '类型名称',
    create_time bigint unsigned  default '0' not null comment '创建时间（毫秒时间戳）',
    update_time bigint unsigned  default '0' not null comment '更新时间（毫秒时间戳）',
    constraint uk_type_language
        unique (type_id, language_id) comment '地标类型ID和语言唯一索引',
    constraint umrah_landmark_type_languages_ibfk_1
        foreign key (type_id) references islamic_content_svc.umrah_landmark_type (id)
            on update cascade on delete cascade
)
    comment '副朝地标类型多语言表';

-- comment on index islamic_content_svc.uk_type_language not supported: 地标类型ID和语言唯一索引

create index idx_language_id
    on islamic_content_svc.umrah_landmark_type_languages (language_id)
    comment '语言ID索引';

-- comment on index islamic_content_svc.idx_language_id not supported: 语言ID索引

create index idx_type_id
    on islamic_content_svc.umrah_landmark_type_languages (type_id)
    comment '地标类型ID索引';

-- comment on index islamic_content_svc.idx_type_id not supported: 地标类型ID索引

